using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Marketplaces.Api.OpenApi;

public sealed class MediaTypeTransformer : IOpenApiDocumentTransformer
{
    public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
    {
        foreach (var path in document.Paths.Values)
        {
            foreach (var operation in path.Operations.Values)
            {
                // Clean up request body media types
                if (operation.RequestBody?.Content != null)
                {
                    var mediaTypes = operation.RequestBody.Content.Keys.ToList();
                    foreach (var mediaType in mediaTypes)
                    {
                        if (mediaType != "application/json")
                        {
                            operation.RequestBody.Content.Remove(mediaType);
                        }
                    }
                }

                // Clean up response media types
                foreach (var response in operation.Responses.Values)
                {
                    if (response.Content != null)
                    {
                        var mediaTypes = response.Content.Keys.ToList();
                        foreach (var mediaType in mediaTypes)
                        {
                            if (mediaType != "application/json")
                            {
                                response.Content.Remove(mediaType);
                            }
                        }
                    }
                }
            }
        }

        return Task.CompletedTask;
    }
}
