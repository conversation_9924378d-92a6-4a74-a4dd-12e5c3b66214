using System.Net.Mime;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models.Identity;
using Marketplaces.Api.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]")]
[Consumes(MediaTypeNames.Application.Json)]
[Produces(MediaTypeNames.Application.Json)]
public class FeedbackController(IEmailSender emailSender, IOptions<AppSettings> options, UserManager<ApplicationUser> userManager) : ControllerBase
{
    private readonly AppSettings _appSettings = options.Value;

    [HttpPost]
    public async Task<IActionResult> CreateFeedback(CreateFeedbackBody body)
    {
        var user = await userManager.GetUserAsync(User);
        if (user == null)
        {
            throw new UnauthorizedException();
        }

        var text = $"Сообщение от пользователя ({user.Email}): {body.Message}";

        await emailSender.SendEmailAsync(_appSettings.DefaultEmail, "Обратная связь", text);
        return Ok();
    }
}