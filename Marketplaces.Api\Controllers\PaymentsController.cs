using System.Net.Mime;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Route("payments")]
[Consumes(MediaTypeNames.Application.Json)]
[Produces(MediaTypeNames.Application.Json)]
public class PaymentsController(DatabaseContext context, YookassaClient yookassaClient) : ShopAbstractController(context)
{
    private const int _subscriptionPrice = 1500;

    [HttpPost]
    // [Authorize(Policy = Policies.EmailConfirmed)]
    [Authorize]
    public async Task<IActionResult> CreatePayment()
    {
        // here try to get not expired payment
        var shop = await GetShop();
        var payment = new Payment(shop.Id, _subscriptionPrice);
        await Context.AddAsync(payment);
        await Context.SaveChangesAsync();

        var user = shop.GetCurrentUser();
        var paymentDto = await yookassaClient.CreatePaymentAsync(user.Email!, user.Id, payment.Id.ToString(), payment.Price);
        payment.Update(paymentDto.Id, paymentDto.ExpiresAt);
        await Context.SaveChangesAsync();
        return Ok(new { paymentDto.Confirmation.ConfirmationUrl });
    }
}